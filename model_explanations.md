# Model Explanations: Reinforcement Learning, Focal Loss, and Backtesting

## Reinforcement Learning Benefits

Reinforcement Learning (RL) provides several key advantages for our financial market prediction model:

1. **Direct Optimization for Trading Performance**: Unlike supervised learning which optimizes for prediction accuracy, RL directly optimizes for trading performance (returns, Sharpe ratio).

2. **Sequential Decision Making**: RL naturally handles the sequential nature of trading decisions, where each action affects future states and rewards.

3. **Adaptive Expert Weighting**: The RL agent learns to dynamically adjust expert weights based on market conditions, effectively learning when to trust each expert.

4. **Risk Management Integration**: RL can incorporate risk constraints directly into the reward function, balancing returns with risk.

The mathematical formulation of our RL approach:

- **State**: Market conditions at time $t$
- **Action**: Expert weights $g_1, g_2, ..., g_N$ where $\sum_{i=1}^{N} g_i = 1$
- **Reward**: Based on prediction accuracy and confidence:

$$R = \begin{cases}
1 + c & \text{if prediction direction is correct} \\
-1 & \text{otherwise}
\end{cases}$$

Where $c = 2 \cdot |p - 0.5|$ is the confidence term and $p$ is the predicted probability.

The RL agent (PPO algorithm) maximizes the expected cumulative discounted reward:

$$J(\theta) = \mathbb{E}_{\pi_\theta} \left[ \sum_{t=0}^{T} \gamma^t R_t \right]$$

Where:
- $\pi_\theta$ is the policy (gating network) with parameters $\theta$
- $\gamma$ is the discount factor
- $R_t$ is the reward at time $t$

## Focal Loss for Class Imbalance

Focal Loss addresses the class imbalance problem common in financial market prediction:

1. **Down-weighting Easy Examples**: Focal loss reduces the contribution of easy-to-classify examples, focusing training on hard examples.

2. **Handling Imbalanced Classes**: Financial data often has imbalanced classes (e.g., more up days than down days), which focal loss handles effectively.

3. **Confidence Calibration**: Focal loss helps prevent overconfidence in predictions, which is crucial for risk management.

The mathematical formulation of Focal Loss:

$$\text{FL}(p_t) = -\alpha_t (1 - p_t)^\gamma \log(p_t)$$

Where:
- $p_t$ is the model's estimated probability for the correct class
- $\alpha_t$ is a weighting factor for class $t$ (helps balance class frequencies)
- $\gamma$ is the focusing parameter (reduces the relative loss for well-classified examples)

For our binary classification problem:
- When $\gamma = 0$, focal loss becomes the standard cross-entropy loss
- As $\gamma$ increases, the effect of down-weighting easy examples becomes stronger
- We typically use $\gamma = 2$ and $\alpha_t = 0.25$ for the minority class

## Backtesting Pseudocode with Equation References

```
FUNCTION backtest_trading_strategy(model, test_dataset, config):
    # Extract configuration parameters
    initial_capital = config.get('backtest', 'initial_capital', 10000)
    position_size = config.get('backtest', 'position_size', 0.1)
    max_loss_per_trade = config.get('backtest', 'max_loss_per_trade', 0.02)
    
    # Initialize tracking variables
    capital = initial_capital
    position = 0  # 0 = no position, 1 = long, -1 = short
    trade_history = []
    equity_curve = [capital]
    
    # Iterate through test dataset
    FOR i = 0 TO len(test_dataset) - 2:
        # Get current sample
        sample = test_dataset[i]
        
        # Get model prediction
        expert_inputs = sample['expert_inputs']
        market_conditions = sample['market_conditions']
        prediction, gating_weights, _ = model(expert_inputs, market_conditions)
        
        # Generate trading signal
        signal = 0
        IF prediction > 0.6:  # Strong bullish signal
            signal = 1
        ELIF prediction < 0.4:  # Strong bearish signal
            signal = -1
        
        # Calculate confidence for position sizing
        # Using equation: c = 2 * |p - 0.5|
        confidence = 2 * abs(prediction - 0.5)
        
        # Dynamic position sizing based on confidence
        position_size_adj = position_size * confidence
        
        # Get current and next price
        current_price = sample['price']
        next_sample = test_dataset[i+1]
        next_price = next_sample['price']
        
        # Calculate return if we have a position
        IF position != 0:
            # Calculate return using log return formula: r_t = ln(P_t/P_{t-1})
            log_return = math.log(next_price / current_price)
            
            # Adjust return based on position direction
            trade_return = position * log_return
            
            # Update capital using return formula: P_final = P_initial * (1 + r)
            capital_change = capital * position_size_adj * (math.exp(trade_return) - 1)
            capital += capital_change
        
        # Update position based on signal
        IF signal != position:
            position = signal
            
            # Record trade in history
            trade_history.append({
                'date': sample['date'],
                'price': current_price,
                'signal': signal,
                'confidence': confidence,
                'capital': capital
            })
        
        # Update equity curve
        equity_curve.append(capital)
    
    # Calculate performance metrics
    total_return = (capital - initial_capital) / initial_capital  # Return formula
    
    # Calculate returns for Sharpe ratio
    returns = [(equity_curve[i+1] - equity_curve[i]) / equity_curve[i] for i in range(len(equity_curve)-1)]
    
    # Calculate Sharpe ratio: (R_p - R_f) / σ_p
    mean_return = statistics.mean(returns)
    std_return = statistics.stdev(returns) if len(returns) > 1 else 1
    sharpe_ratio = (mean_return / std_return) * math.sqrt(252)  # Annualized
    
    # Calculate maximum drawdown using MDD formula
    max_drawdown = calculate_max_drawdown(equity_curve)
    
    RETURN {
        'total_return': total_return * 100,  # Convert to percentage
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'trade_history': trade_history,
        'equity_curve': equity_curve
    }

FUNCTION calculate_max_drawdown(equity_curve):
    # Implements the MDD formula: MDD = max_t (max_τ∈(0,t) (V_τ - V_t) / V_τ)
    peak = equity_curve[0]
    max_drawdown = 0
    
    FOR value IN equity_curve:
        IF value > peak:
            peak = value
        drawdown = (peak - value) / peak
        max_drawdown = max(max_drawdown, drawdown)
    
    RETURN max_drawdown
```

