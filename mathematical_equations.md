# Mathematical Equations in Financial Market Prediction Model

This document provides the mathematical foundation for the mixture of experts model used for financial market prediction.

## 1. Data Preprocessing

### Log Returns
Instead of using raw prices, we use log returns to make the data stationary:

$$r_t = \ln\left(\frac{P_t}{P_{t-1}}\right)$$

Where:
- $r_t$ is the log return at time $t$
- $P_t$ is the price at time $t$
- $P_{t-1}$ is the price at time $t-1$

### Binary Classification Target
We transform the prediction task into a binary classification problem:

$$y_t = \begin{cases}
1 & \text{if } P_{t+h} > P_t \\
0 & \text{otherwise}
\end{cases}$$

Where:
- $y_t$ is the binary target at time $t$
- $P_{t+h}$ is the price at time $t+h$ (forecast horizon)
- $P_t$ is the price at time $t$

## 2. Expert Models

### LSTM Expert (Short-term)
The LSTM processes sequential data with the following equations:

$$f_t = \sigma(W_f \cdot [h_{t-1}, x_t] + b_f)$$
$$i_t = \sigma(W_i \cdot [h_{t-1}, x_t] + b_i)$$
$$\tilde{C}_t = \tanh(W_C \cdot [h_{t-1}, x_t] + b_C)$$
$$C_t = f_t * C_{t-1} + i_t * \tilde{C}_t$$
$$o_t = \sigma(W_o \cdot [h_{t-1}, x_t] + b_o)$$
$$h_t = o_t * \tanh(C_t)$$

Where:
- $f_t$ is the forget gate
- $i_t$ is the input gate
- $\tilde{C}_t$ is the candidate cell state
- $C_t$ is the cell state
- $o_t$ is the output gate
- $h_t$ is the hidden state
- $W$ and $b$ are learnable parameters
- $\sigma$ is the sigmoid function

### WaveNet Expert (Medium-term)
The WaveNet uses dilated causal convolutions:

$$z = \tanh(W_{f,k} * x) \odot \sigma(W_{g,k} * x)$$

Where:
- $W_{f,k}$ and $W_{g,k}$ are convolutional filters
- $*$ denotes the convolution operation
- $\odot$ is element-wise multiplication
- $\sigma$ is the sigmoid function

### Transformer Expert (Long-term)
The Transformer uses self-attention mechanisms:

$$\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$$

Where:
- $Q$ is the query matrix
- $K$ is the key matrix
- $V$ is the value matrix
- $d_k$ is the dimension of the keys

## 3. Gating Network

The gating network computes weights for each expert:

$$g = \text{softmax}(W_g \cdot \text{ReLU}(W_h \cdot x + b_h) + b_g)$$

Where:
- $g$ is the vector of expert weights
- $W_g$, $W_h$, $b_g$, $b_h$ are learnable parameters
- $x$ is the market conditions input

## 4. Mixture of Experts

The final prediction is a weighted sum of expert predictions:

$$\hat{y} = \sum_{i=1}^{N} g_i \cdot y_i$$

Where:
- $\hat{y}$ is the final prediction
- $g_i$ is the weight for expert $i$
- $y_i$ is the prediction from expert $i$
- $N$ is the number of experts (3 in our case)

## 5. Loss Functions

### Focal Loss
We use focal loss to address class imbalance:

$$\text{FL}(p_t) = -\alpha_t (1 - p_t)^\gamma \log(p_t)$$

Where:
- $p_t$ is the model's estimated probability for the correct class
- $\alpha_t$ is a weighting factor for class $t$
- $\gamma$ is the focusing parameter

### Reinforcement Learning Reward
The reward function for RL training:

$$R = \begin{cases}
1 + c & \text{if prediction direction is correct} \\
-1 & \text{otherwise}
\end{cases}$$

Where:
- $c = 2 \cdot |p - 0.5|$ is the confidence term
- $p$ is the predicted probability

## 6. Evaluation Metrics

### Accuracy
$$\text{Accuracy} = \frac{\text{Number of correct predictions}}{\text{Total number of predictions}}$$

### Precision
$$\text{Precision} = \frac{\text{True Positives}}{\text{True Positives + False Positives}}$$

### Recall
$$\text{Recall} = \frac{\text{True Positives}}{\text{True Positives + False Negatives}}$$

### F1 Score
$$\text{F1} = 2 \cdot \frac{\text{Precision} \cdot \text{Recall}}{\text{Precision} + \text{Recall}}$$

## 7. Backtesting Metrics

### Return
$$\text{Return} = \frac{P_{\text{final}} - P_{\text{initial}}}{P_{\text{initial}}}$$

### Sharpe Ratio
$$\text{Sharpe Ratio} = \frac{R_p - R_f}{\sigma_p}$$

Where:
- $R_p$ is the portfolio return
- $R_f$ is the risk-free rate
- $\sigma_p$ is the portfolio standard deviation

### Maximum Drawdown
$$\text{MDD} = \max_t \left( \max_{\tau \in (0,t)} \frac{V_\tau - V_t}{V_\tau} \right)$$

Where:
- $V_t$ is the value at time $t$